package data

// Storage 存储接口 - 简化的存储架构
type Storage interface {
	// 主机管理
	SaveHost(host HostInfo) error
	LoadHosts() ([]HostInfo, error)
	GetHost(hostID string) (*HostInfo, error)
	DeleteHost(hostID string) error

	// 主机组管理
	SaveGroup(group HostGroup) error
	LoadGroups() ([]HostGroup, error)
	GetGroup(groupID string) (*HostGroup, error)
	DeleteGroup(groupID string) error

	// 扫描任务管理
	SaveTask(task ScanTask) error
	LoadTasks() ([]ScanTask, error)
	DeleteTask(taskID string) error

	// 扫描结果管理
	SaveScanResult(result ScanResult) error
	LoadScanResults() ([]ScanResult, error)
	LoadScanResultsByTask(taskID string) ([]ScanResult, error)
	DeleteScanResultsByTask(taskID string) error

	// 关闭连接
	Close() error
}

// NewStorage 创建存储实例 - 默认使用SQLite
func NewStorage() (Storage, error) {
	return NewSQLiteStorage("")
}

// NewStorageWithPath 创建指定路径的存储实例
func NewStorageWithPath(dbPath string) (Storage, error) {
	return NewSQLiteStorage(dbPath)
}
