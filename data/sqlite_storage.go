package data

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

// SQLiteStorage SQLite数据库存储
type SQLiteStorage struct {
	db *sql.DB
}

// NewSQLiteStorage 创建SQLite存储
func NewSQLiteStorage(dbPath string) (*SQLiteStorage, error) {
	if dbPath == "" {
		dbPath = "./lcheck.db"
	}

	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, fmt.Errorf("打开数据库失败: %v", err)
	}

	storage := &SQLiteStorage{db: db}
	if err := storage.initTables(); err != nil {
		return nil, fmt.Errorf("初始化数据库表失败: %v", err)
	}

	return storage, nil
}

// Close 关闭数据库连接
func (s *SQLiteStorage) Close() error {
	return s.db.Close()
}

// initTables 初始化数据库表
func (s *SQLiteStorage) initTables() error {
	// 主机表
	hostTable := `
	CREATE TABLE IF NOT EXISTS hosts (
		id TEXT PRIMARY KEY,
		name TEXT NOT NULL,
		host TEXT NOT NULL,
		port TEXT NOT NULL,
		username TEXT NOT NULL,
		password TEXT NOT NULL,
		status TEXT DEFAULT '',
		last_scan DATETIME,
		description TEXT DEFAULT '',
		tags TEXT DEFAULT '[]',
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	// 主机组表
	groupTable := `
	CREATE TABLE IF NOT EXISTS host_groups (
		id TEXT PRIMARY KEY,
		name TEXT NOT NULL,
		description TEXT DEFAULT '',
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	// 主机组内主机表（独立存储，不与外部主机共享）
	groupHostTable := `
	CREATE TABLE IF NOT EXISTS group_hosts (
		id TEXT PRIMARY KEY,
		group_id TEXT NOT NULL,
		name TEXT NOT NULL,
		host TEXT NOT NULL,
		port TEXT DEFAULT '22',
		username TEXT NOT NULL,
		password TEXT NOT NULL,
		description TEXT DEFAULT '',
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (group_id) REFERENCES host_groups(id) ON DELETE CASCADE
	);`

	// 扫描任务表
	taskTable := `
	CREATE TABLE IF NOT EXISTS scan_tasks (
		id TEXT PRIMARY KEY,
		name TEXT NOT NULL,
		status TEXT DEFAULT 'pending',
		progress REAL DEFAULT 0,
		host_ids TEXT NOT NULL,
		template TEXT DEFAULT '',
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		started_at DATETIME,
		completed_at DATETIME,
		error TEXT DEFAULT ''
	);`

	// 扫描结果表
	resultTable := `
	CREATE TABLE IF NOT EXISTS scan_results (
		id TEXT PRIMARY KEY,
		task_id TEXT,
		host_id TEXT NOT NULL,
		host_name TEXT NOT NULL,
		host TEXT NOT NULL,
		status TEXT NOT NULL,
		progress INTEGER DEFAULT 0,
		total_checks INTEGER DEFAULT 0,
		passed_checks INTEGER DEFAULT 0,
		failed_checks INTEGER DEFAULT 0,
		warning_checks INTEGER DEFAULT 0,
		skipped_checks INTEGER DEFAULT 0,
		total_score INTEGER DEFAULT 0,
		start_time DATETIME NOT NULL,
		end_time DATETIME,
		check_results TEXT DEFAULT '[]',
		system_info TEXT DEFAULT '{}',
		metadata TEXT DEFAULT '{}',
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (task_id) REFERENCES scan_tasks(id) ON DELETE CASCADE,
		FOREIGN KEY (host_id) REFERENCES hosts(id) ON DELETE CASCADE
	);`

	tables := []string{hostTable, groupTable, groupHostTable, taskTable, resultTable}
	for _, table := range tables {
		if _, err := s.db.Exec(table); err != nil {
			return fmt.Errorf("创建表失败: %v", err)
		}
	}

	return nil
}

// ========== 主机管理 ==========

// SaveHost 保存主机
func (s *SQLiteStorage) SaveHost(host HostInfo) error {
	tagsJSON, _ := json.Marshal(host.Tags)

	query := `
	INSERT OR REPLACE INTO hosts 
	(id, name, host, port, username, password, status, last_scan, description, tags, created_at, updated_at)
	VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	now := time.Now()
	createdAt := host.CreatedAt
	if createdAt.IsZero() {
		createdAt = now
	}

	_, err := s.db.Exec(query, host.ID, host.Name, host.Host, host.Port,
		host.Username, host.Password, host.Status, host.LastScan,
		host.Description, string(tagsJSON), createdAt, now)

	return err
}

// LoadHosts 加载所有主机
func (s *SQLiteStorage) LoadHosts() ([]HostInfo, error) {
	query := `SELECT id, name, host, port, username, password, status, 
			  last_scan, description, tags, created_at, updated_at FROM hosts`

	rows, err := s.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var hosts []HostInfo
	for rows.Next() {
		var host HostInfo
		var tagsJSON string
		var lastScan, createdAt, updatedAt sql.NullTime

		err := rows.Scan(&host.ID, &host.Name, &host.Host, &host.Port,
			&host.Username, &host.Password, &host.Status, &lastScan,
			&host.Description, &tagsJSON, &createdAt, &updatedAt)
		if err != nil {
			continue
		}

		if lastScan.Valid {
			host.LastScan = lastScan.Time
		}
		if createdAt.Valid {
			host.CreatedAt = createdAt.Time
		}
		if updatedAt.Valid {
			host.UpdatedAt = updatedAt.Time
		}

		json.Unmarshal([]byte(tagsJSON), &host.Tags)
		hosts = append(hosts, host)
	}

	return hosts, nil
}

// GetHost 获取单个主机
func (s *SQLiteStorage) GetHost(hostID string) (*HostInfo, error) {
	query := `SELECT id, name, host, port, username, password, status, 
			  last_scan, description, tags, created_at, updated_at 
			  FROM hosts WHERE id = ?`

	var host HostInfo
	var tagsJSON string
	var lastScan, createdAt, updatedAt sql.NullTime

	err := s.db.QueryRow(query, hostID).Scan(&host.ID, &host.Name, &host.Host,
		&host.Port, &host.Username, &host.Password, &host.Status, &lastScan,
		&host.Description, &tagsJSON, &createdAt, &updatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("主机 %s 不存在", hostID)
		}
		return nil, err
	}

	if lastScan.Valid {
		host.LastScan = lastScan.Time
	}
	if createdAt.Valid {
		host.CreatedAt = createdAt.Time
	}
	if updatedAt.Valid {
		host.UpdatedAt = updatedAt.Time
	}

	json.Unmarshal([]byte(tagsJSON), &host.Tags)
	return &host, nil
}

// DeleteHost 删除主机
func (s *SQLiteStorage) DeleteHost(hostID string) error {
	_, err := s.db.Exec("DELETE FROM hosts WHERE id = ?", hostID)
	return err
}

// ========== 主机组管理 ==========

// SaveGroup 保存主机组
func (s *SQLiteStorage) SaveGroup(group HostGroup) error {
	tx, err := s.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 保存主机组基本信息
	now := time.Now()
	createdAt := group.CreatedAt
	if createdAt.IsZero() {
		createdAt = now
	}

	_, err = tx.Exec(`
		INSERT OR REPLACE INTO host_groups 
		(id, name, description, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?)`,
		group.ID, group.Name, group.Description, createdAt, now)
	if err != nil {
		return err
	}

	// 删除旧的主机组内主机
	_, err = tx.Exec("DELETE FROM group_hosts WHERE group_id = ?", group.ID)
	if err != nil {
		return err
	}

	// 保存主机组内的主机（独立存储）
	hostQuery := `
	INSERT INTO group_hosts
	(id, group_id, name, host, port, username, password, description, created_at, updated_at)
	VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	for _, host := range group.Hosts {
		hostID := host.ID
		if hostID == "" {
			hostID = fmt.Sprintf("group_host_%s_%d", group.ID, time.Now().UnixNano())
		}

		hostCreatedAt := host.CreatedAt
		if hostCreatedAt.IsZero() {
			hostCreatedAt = now
		}

		_, err = tx.Exec(hostQuery, hostID, group.ID, host.Name, host.Host,
			host.Port, host.Username, host.Password, host.Description, hostCreatedAt, now)
		if err != nil {
			return err
		}
	}

	return tx.Commit()
}

// LoadGroups 加载所有主机组
func (s *SQLiteStorage) LoadGroups() ([]HostGroup, error) {
	// 先加载主机组基本信息
	query := `SELECT id, name, description, created_at, updated_at FROM host_groups`
	rows, err := s.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var groups []HostGroup
	for rows.Next() {
		var group HostGroup
		var createdAt, updatedAt sql.NullTime

		err := rows.Scan(&group.ID, &group.Name, &group.Description, &createdAt, &updatedAt)
		if err != nil {
			continue
		}

		if createdAt.Valid {
			group.CreatedAt = createdAt.Time
		}
		if updatedAt.Valid {
			group.UpdatedAt = updatedAt.Time
		}

		// 加载组内主机
		hosts, err := s.loadGroupHosts(group.ID)
		if err == nil {
			group.Hosts = hosts
		}

		groups = append(groups, group)
	}

	return groups, nil
}

// loadGroupHosts 加载主机组内的主机（从独立表中加载）
func (s *SQLiteStorage) loadGroupHosts(groupID string) ([]HostInfo, error) {
	query := `
	SELECT id, name, host, port, username, password, description, created_at, updated_at
	FROM group_hosts
	WHERE group_id = ?`

	rows, err := s.db.Query(query, groupID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var hosts []HostInfo
	for rows.Next() {
		var host HostInfo
		var createdAt, updatedAt sql.NullTime

		err := rows.Scan(&host.ID, &host.Name, &host.Host, &host.Port,
			&host.Username, &host.Password, &host.Description, &createdAt, &updatedAt)
		if err != nil {
			continue
		}

		if createdAt.Valid {
			host.CreatedAt = createdAt.Time
		}
		if updatedAt.Valid {
			host.UpdatedAt = updatedAt.Time
		}

		// 主机组内的主机没有状态、扫描时间、标签等字段
		host.Status = ""
		host.Tags = []string{}

		hosts = append(hosts, host)
	}

	return hosts, nil
}

// GetGroup 获取单个主机组
func (s *SQLiteStorage) GetGroup(groupID string) (*HostGroup, error) {
	query := `SELECT id, name, description, created_at, updated_at FROM host_groups WHERE id = ?`

	var group HostGroup
	var createdAt, updatedAt sql.NullTime

	err := s.db.QueryRow(query, groupID).Scan(&group.ID, &group.Name, &group.Description, &createdAt, &updatedAt)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("主机组 %s 不存在", groupID)
		}
		return nil, err
	}

	if createdAt.Valid {
		group.CreatedAt = createdAt.Time
	}
	if updatedAt.Valid {
		group.UpdatedAt = updatedAt.Time
	}

	// 加载组内主机
	hosts, err := s.loadGroupHosts(groupID)
	if err == nil {
		group.Hosts = hosts
	}

	return &group, nil
}

// DeleteGroup 删除主机组
func (s *SQLiteStorage) DeleteGroup(groupID string) error {
	_, err := s.db.Exec("DELETE FROM host_groups WHERE id = ?", groupID)
	return err
}

// ========== 扫描任务管理 ==========

// SaveTask 保存扫描任务
func (s *SQLiteStorage) SaveTask(task ScanTask) error {
	hostIDsJSON, _ := json.Marshal(task.HostIDs)

	fmt.Printf("保存任务 - 任务: %s, HostIDs数量: %d, HostIDs: %v, JSON: %s\n",
		task.Name, len(task.HostIDs), task.HostIDs, string(hostIDsJSON))

	query := `
	INSERT OR REPLACE INTO scan_tasks
	(id, name, status, progress, host_ids, template, created_at, started_at, completed_at, error)
	VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err := s.db.Exec(query, task.ID, task.Name, task.Status, task.Progress,
		string(hostIDsJSON), task.Template, task.CreatedAt, task.StartedAt,
		task.CompletedAt, task.Error)

	return err
}

// LoadTasks 加载所有扫描任务
func (s *SQLiteStorage) LoadTasks() ([]ScanTask, error) {
	query := `SELECT id, name, status, progress, host_ids, template, created_at,
			  started_at, completed_at, error FROM scan_tasks ORDER BY created_at DESC`

	rows, err := s.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tasks []ScanTask
	for rows.Next() {
		var task ScanTask
		var hostIDsJSON string
		var startedAt, completedAt sql.NullTime

		err := rows.Scan(&task.ID, &task.Name, &task.Status, &task.Progress,
			&hostIDsJSON, &task.Template, &task.CreatedAt, &startedAt,
			&completedAt, &task.Error)
		if err != nil {
			continue
		}

		if startedAt.Valid {
			task.StartedAt = &startedAt.Time
		}
		if completedAt.Valid {
			task.CompletedAt = &completedAt.Time
		}

		json.Unmarshal([]byte(hostIDsJSON), &task.HostIDs)

		fmt.Printf("加载任务 - 任务: %s, HostIDs数量: %d, HostIDs: %v, JSON: %s\n",
			task.Name, len(task.HostIDs), task.HostIDs, hostIDsJSON)

		// 加载任务的扫描结果
		results, _ := s.LoadScanResultsByTask(task.ID)
		task.Results = results

		tasks = append(tasks, task)
	}

	return tasks, nil
}

// DeleteTask 删除扫描任务
func (s *SQLiteStorage) DeleteTask(taskID string) error {
	_, err := s.db.Exec("DELETE FROM scan_tasks WHERE id = ?", taskID)
	return err
}

// ========== 扫描结果管理 ==========

// SaveScanResult 保存扫描结果
func (s *SQLiteStorage) SaveScanResult(result ScanResult) error {
	checkResultsJSON, _ := json.Marshal(result.CheckResults)
	systemInfoJSON, _ := json.Marshal(result.SystemInfo)
	metadataJSON, _ := json.Marshal(result.Metadata)

	query := `
	INSERT OR REPLACE INTO scan_results
	(id, task_id, host_id, host_name, host, status, progress, total_checks,
	 passed_checks, failed_checks, warning_checks, skipped_checks, total_score,
	 start_time, end_time, check_results, system_info, metadata, created_at)
	VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err := s.db.Exec(query, result.ID, "", result.HostID,
		result.HostName, result.Host, result.Status, result.Progress,
		result.TotalChecks, result.PassedChecks, result.FailedChecks,
		result.WarningChecks, result.SkippedChecks, result.TotalScore,
		result.StartTime, result.EndTime, string(checkResultsJSON),
		string(systemInfoJSON), string(metadataJSON), time.Now())

	return err
}

// UpdateScanResultTaskID 更新扫描结果的任务ID关联
func (s *SQLiteStorage) UpdateScanResultTaskID(resultID, taskID string) error {
	query := `UPDATE scan_results SET task_id = ? WHERE id = ?`
	_, err := s.db.Exec(query, taskID, resultID)
	return err
}

// LoadScanResults 加载扫描结果
func (s *SQLiteStorage) LoadScanResults() ([]ScanResult, error) {
	query := `SELECT id, task_id, host_id, host_name, host, status, progress,
			  total_checks, passed_checks, failed_checks, warning_checks,
			  skipped_checks, total_score, start_time, end_time, check_results,
			  system_info, metadata, created_at FROM scan_results ORDER BY created_at DESC`

	rows, err := s.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []ScanResult
	for rows.Next() {
		var result ScanResult
		var checkResultsJSON, systemInfoJSON, metadataJSON string
		var endTime, createdAt sql.NullTime

		var taskID string
		err := rows.Scan(&result.ID, &taskID, &result.HostID, &result.HostName,
			&result.Host, &result.Status, &result.Progress, &result.TotalChecks,
			&result.PassedChecks, &result.FailedChecks, &result.WarningChecks,
			&result.SkippedChecks, &result.TotalScore, &result.StartTime, &endTime,
			&checkResultsJSON, &systemInfoJSON, &metadataJSON, &createdAt)
		if err != nil {
			continue
		}

		if endTime.Valid {
			result.EndTime = endTime.Time
		}

		json.Unmarshal([]byte(checkResultsJSON), &result.CheckResults)
		json.Unmarshal([]byte(systemInfoJSON), &result.SystemInfo)
		json.Unmarshal([]byte(metadataJSON), &result.Metadata)
		results = append(results, result)
	}

	return results, nil
}

// LoadScanResultsByTask 根据任务ID加载扫描结果
func (s *SQLiteStorage) LoadScanResultsByTask(taskID string) ([]ScanResult, error) {
	query := `SELECT id, task_id, host_id, host_name, host, status, progress,
			  total_checks, passed_checks, failed_checks, warning_checks,
			  skipped_checks, total_score, start_time, end_time, check_results,
			  system_info, metadata, created_at FROM scan_results WHERE task_id = ? ORDER BY created_at DESC`

	rows, err := s.db.Query(query, taskID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []ScanResult
	for rows.Next() {
		var result ScanResult
		var checkResultsJSON, systemInfoJSON, metadataJSON string
		var endTime, createdAt sql.NullTime

		var taskID string
		err := rows.Scan(&result.ID, &taskID, &result.HostID, &result.HostName,
			&result.Host, &result.Status, &result.Progress, &result.TotalChecks,
			&result.PassedChecks, &result.FailedChecks, &result.WarningChecks,
			&result.SkippedChecks, &result.TotalScore, &result.StartTime, &endTime,
			&checkResultsJSON, &systemInfoJSON, &metadataJSON, &createdAt)
		if err != nil {
			continue
		}

		if endTime.Valid {
			result.EndTime = endTime.Time
		}

		json.Unmarshal([]byte(checkResultsJSON), &result.CheckResults)
		json.Unmarshal([]byte(systemInfoJSON), &result.SystemInfo)
		json.Unmarshal([]byte(metadataJSON), &result.Metadata)
		results = append(results, result)
	}

	return results, nil
}

// DeleteScanResultsByTask 删除指定任务的所有扫描结果
func (s *SQLiteStorage) DeleteScanResultsByTask(taskID string) error {
	query := `DELETE FROM scan_results WHERE task_id = ?`
	_, err := s.db.Exec(query, taskID)
	return err
}
