package checkers

import (
	"fmt"
	"strings"

	"lcheck/data"
)

// SSHChecker SSH安全检查器 - 专门负责SSH相关的安全检查
type SSHChecker struct {
	name     string
	category string
}

// NewSSHChecker 创建SSH检查器
func NewSSHChecker() *SSHChecker {
	return &SSHChecker{
		name:     "SSH安全检查器",
		category: "SSH安全",
	}
}

// GetName 获取检查器名称
func (sc *SSHChecker) GetName() string {
	return sc.name
}

// GetCategory 获取检查器类别
func (sc *SSHChecker) GetCategory() string {
	return sc.category
}

// GetChecks 获取SSH相关的所有检查项
func (sc *SSHChecker) GetChecks() []data.BaselineCheck {
	return []data.BaselineCheck{
		{
			ID:          "SSH_ROOT_LOGIN",
			Name:        "SSH Root登录检查",
			Category:    "SSH安全",
			Description: "检查是否禁用SSH Root登录",
			Risk:        "高",
			Solution:    "在/etc/ssh/sshd_config中设置PermitRootLogin no",
			Reference:   "CIS Linux Benchmark 5.2.8",
			CheckFunc: func(client interface{}, host data.HostInfo) data.BaselineCheckResult {
				return sc.checkRootLogin(client.(SSHExecutor), host)
			},
		},
		{
			ID:          "SSH_CONFIG",
			Name:        "SSH配置信息收集",
			Category:    "SSH安全",
			Description: "收集SSH服务的详细配置信息",
			Risk:        "信息",
			Solution:    "根据收集的配置信息进行安全加固",
			Reference:   "SSH安全配置最佳实践",
			CheckFunc: func(client interface{}, host data.HostInfo) data.BaselineCheckResult {
				return sc.checkSSHConfig(client.(SSHExecutor), host)
			},
		},
	}
}

// RunCheck 执行特定SSH检查项
func (sc *SSHChecker) RunCheck(sshClient SSHExecutor, host data.HostInfo, checkID string) (*data.BaselineCheckResult, error) {
	checks := sc.GetChecks()
	for _, check := range checks {
		if check.ID == checkID {
			result := check.CheckFunc(sshClient, host)
			result.CheckName = check.Name
			result.Category = check.Category
			result.Description = check.Description
			result.Risk = check.Risk
			result.Solution = check.Solution
			result.Reference = check.Reference
			return &result, nil
		}
	}
	return nil, fmt.Errorf("SSH检查项 %s 不存在", checkID)
}

// RunAllChecks 执行所有SSH检查项
func (sc *SSHChecker) RunAllChecks(sshClient SSHExecutor, host data.HostInfo) ([]data.BaselineCheckResult, error) {
	var results []data.BaselineCheckResult
	checks := sc.GetChecks()

	for _, check := range checks {
		// 添加panic恢复机制
		func() {
			defer func() {
				if r := recover(); r != nil {
					fmt.Printf("SSH检查项 %s 执行时发生panic: %v\n", check.ID, r)
					// 创建一个错误结果
					errorResult := data.BaselineCheckResult{
						ID:          check.ID,
						CheckName:   check.Name,
						Category:    check.Category,
						Status:      "失败",
						Score:       0,
						Risk:        check.Risk,
						Description: check.Description,
						Details:     fmt.Sprintf("检查执行失败: %v", r),
						Solution:    check.Solution,
						Reference:   check.Reference,
						Error:       fmt.Sprintf("panic: %v", r),
					}
					results = append(results, errorResult)
				}
			}()

			result := check.CheckFunc(sshClient, host)
			result.CheckName = check.Name
			result.Category = check.Category
			result.Description = check.Description
			result.Risk = check.Risk
			result.Solution = check.Solution
			result.Reference = check.Reference
			results = append(results, result)
		}()
	}

	return results, nil
}

// checkRootLogin 检查SSH Root登录配置
func (sc *SSHChecker) checkRootLogin(sshClient SSHExecutor, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		ID:      "SSH_ROOT_LOGIN",
		Command: "(grep -E '^[[:space:]]*PermitRootLogin' /etc/ssh/sshd_config 2>/dev/null) || (grep -E '^[[:space:]]*PermitRootLogin' /etc/sshd_config 2>/dev/null) || echo 'SSH config not found'",
	}

	output, err := sshClient.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output

	if result.Metadata == nil {
		result.Metadata = make(map[string]string)
	}

	// 解析配置
	if strings.Contains(output, "PermitRootLogin no") {
		result.Status = "通过"
		result.Score = 100
		result.Details = "SSH Root登录已禁用"
		result.Metadata["permitRootLogin"] = "no"
	} else if strings.Contains(output, "PermitRootLogin yes") {
		result.Status = "失败"
		result.Score = 0
		result.Details = "SSH Root登录未禁用，存在安全风险"
		result.Metadata["permitRootLogin"] = "yes"
	} else {
		result.Status = "警告"
		result.Score = 50
		result.Details = "SSH Root登录配置不明确，使用默认值"
		result.Metadata["permitRootLogin"] = "default"
	}

	return result
}

// checkSSHConfig 检查SSH配置信息
func (sc *SSHChecker) checkSSHConfig(sshClient SSHExecutor, host data.HostInfo) data.BaselineCheckResult {
	result := data.BaselineCheckResult{
		ID:      "SSH_CONFIG",
		Command: "(grep -E '^[[:space:]]*(PermitRootLogin|PasswordAuthentication|PermitEmptyPasswords|PubkeyAuthentication|Protocol|MaxAuthTries)' /etc/ssh/sshd_config 2>/dev/null) || (grep -E '^[[:space:]]*(PermitRootLogin|PasswordAuthentication|PermitEmptyPasswords|PubkeyAuthentication|Protocol|MaxAuthTries)' /etc/sshd_config 2>/dev/null) || echo 'SSH config not accessible'",
	}

	output, err := sshClient.ExecuteCommand(host, result.Command)
	if err != nil {
		result.Status = "失败"
		result.Error = err.Error()
		result.Score = 0
		return result
	}

	result.Output = output

	if result.Metadata == nil {
		result.Metadata = make(map[string]string)
	}

	if strings.Contains(output, "SSH config not accessible") {
		result.Status = "警告"
		result.Score = 30
		result.Details = "无法访问SSH配置文件"
		// 设置默认值
		result.Metadata["permitRootLogin"] = "yes"
		result.Metadata["passwordAuthentication"] = "yes"
		result.Metadata["permitEmptyPasswords"] = "no"
		result.Metadata["pubkeyAuthentication"] = "yes"
		result.Metadata["protocol"] = "2"
		result.Metadata["maxAuthTries"] = "6"
	} else {
		result.Status = "信息收集"
		result.Score = 100
		result.Details = "SSH配置信息收集完成"

		// 初始化默认值
		result.Metadata["permitRootLogin"] = "yes"
		result.Metadata["passwordAuthentication"] = "yes"
		result.Metadata["permitEmptyPasswords"] = "no"
		result.Metadata["pubkeyAuthentication"] = "yes"
		result.Metadata["protocol"] = "2"
		result.Metadata["maxAuthTries"] = "6"

		// 解析SSH配置项
		lines := strings.Split(output, "\n")
		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line == "" || strings.HasPrefix(line, "#") {
				continue
			}

			// 解析各种SSH配置参数
			parts := strings.Fields(line)
			if len(parts) >= 2 {
				key := strings.ToLower(parts[0])
				value := strings.ToLower(parts[1])

				switch key {
				case "permitrootlogin":
					result.Metadata["permitRootLogin"] = value
				case "passwordauthentication":
					result.Metadata["passwordAuthentication"] = value
				case "permitemptypasswords":
					result.Metadata["permitEmptyPasswords"] = value
				case "pubkeyauthentication":
					result.Metadata["pubkeyAuthentication"] = value
				case "protocol":
					result.Metadata["protocol"] = value
				case "maxauthtries":
					result.Metadata["maxAuthTries"] = value
				}
			}
		}
	}

	return result
}
