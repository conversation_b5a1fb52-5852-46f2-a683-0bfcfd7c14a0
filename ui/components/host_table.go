package components

import (
	"fmt"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"lcheck/data"
)

// HostTable 主机列表组件 - 专门负责主机列表的显示和交互
// 遵循模块化规范：单一职责原则，只负责主机列表功能，与GroupList保持一致的接口设计
type HostTable struct {
	list          *widget.List
	container     *fyne.Container
	hosts         []data.HostInfo
	selectedHost  *data.HostInfo
	onSelection   func(*data.HostInfo)
	onDoubleClick func(*data.HostInfo)
}

// NewHostTable 创建主机列表组件
// 遵循模块化规范：使用New前缀的构造函数
func NewHostTable() *HostTable {
	ht := &HostTable{
		hosts: make([]data.HostInfo, 0),
	}

	ht.buildList()
	ht.container = container.NewBorder(nil, nil, nil, nil, ht.list)

	return ht
}

// buildList 构建主机列表
// 遵循模块化规范：私有函数，职责单一，与GroupList保持一致的实现方式
func (ht *HostTable) buildList() {
	// 使用List组件显示主机，与GroupList保持一致的实现方式
	ht.list = widget.NewList(
		func() int {
			return len(ht.hosts)
		},
		func() fyne.CanvasObject {
			return widget.NewLabel("")
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id >= 0 && id < len(ht.hosts) {
				host := ht.hosts[id]
				label := obj.(*widget.Label)

				// 确保数据不为空，提供默认值
				name := host.Name
				if name == "" {
					name = "未命名主机"
				}

				hostAddr := host.Host
				if hostAddr == "" {
					hostAddr = "未设置地址"
				}

				port := host.Port
				if port == "" {
					port = "22"
				}

				username := host.Username
				if username == "" {
					username = "未设置用户"
				}

				// 显示主机信息：名称 (地址:端口) - 用户名
				text := fmt.Sprintf("%s (%s:%s) - %s", name, hostAddr, port, username)
				label.SetText(text)

				// 设置文本换行，防止长文本被截断
				label.Wrapping = fyne.TextWrapWord
			}
		},
	)

	// 设置选择回调
	ht.list.OnSelected = func(id widget.ListItemID) {
		if id >= 0 && id < len(ht.hosts) {
			ht.selectedHost = &ht.hosts[id]
			if ht.onSelection != nil {
				ht.onSelection(ht.selectedHost)
			}
		}
	}

	// 设置取消选择回调
	ht.list.OnUnselected = func(id widget.ListItemID) {
		ht.selectedHost = nil
		if ht.onSelection != nil {
			ht.onSelection(nil)
		}
	}
}

// GetContainer 获取容器
// 遵循模块化规范：公共接口，返回UI容器
func (ht *HostTable) GetContainer() *fyne.Container {
	return ht.container
}

// SetHosts 设置主机数据
// 遵循模块化规范：数据更新接口，与GroupList保持一致
func (ht *HostTable) SetHosts(hosts []data.HostInfo) {
	ht.hosts = hosts

	// 清除选中状态，防止默认选中第一项
	ht.selectedHost = nil
	ht.list.UnselectAll()
	ht.list.Refresh()

	// 触发取消选择回调，确保UI状态一致
	if ht.onSelection != nil {
		ht.onSelection(nil)
	}
}

// GetHosts 获取主机数据
// 遵循模块化规范：数据访问接口
func (ht *HostTable) GetHosts() []data.HostInfo {
	return ht.hosts
}

// GetSelectedHost 获取选中的主机
// 遵循模块化规范：状态访问接口
func (ht *HostTable) GetSelectedHost() *data.HostInfo {
	return ht.selectedHost
}

// SetOnSelection 设置选择回调
// 遵循模块化规范：事件处理接口
func (ht *HostTable) SetOnSelection(callback func(*data.HostInfo)) {
	ht.onSelection = callback
}

// SetOnDoubleClick 设置双击回调
// 遵循模块化规范：事件处理接口
func (ht *HostTable) SetOnDoubleClick(callback func(*data.HostInfo)) {
	ht.onDoubleClick = callback

	// 为列表设置双击事件
	ht.list.OnSelected = func(id widget.ListItemID) {
		if id >= 0 && id < len(ht.hosts) {
			ht.selectedHost = &ht.hosts[id]
			if ht.onSelection != nil {
				ht.onSelection(ht.selectedHost)
			}
		}
	}
}

// ClearSelection 清除选择
// 遵循模块化规范：状态管理接口
func (ht *HostTable) ClearSelection() {
	ht.selectedHost = nil
	ht.list.UnselectAll()
}

// Refresh 刷新列表显示
// 遵循模块化规范：UI更新接口
func (ht *HostTable) Refresh() {
	ht.list.Refresh()
}

// UpdateHost 更新指定主机的显示
// 遵循模块化规范：增量更新接口
func (ht *HostTable) UpdateHost(hostID string, updatedHost data.HostInfo) {
	for i, host := range ht.hosts {
		if host.ID == hostID {
			ht.hosts[i] = updatedHost
			ht.list.Refresh()
			break
		}
	}
}

// RemoveHost 从列表中移除指定主机
// 遵循模块化规范：数据操作接口
func (ht *HostTable) RemoveHost(hostID string) {
	for i, host := range ht.hosts {
		if host.ID == hostID {
			ht.hosts = append(ht.hosts[:i], ht.hosts[i+1:]...)
			ht.list.Refresh()

			// 如果删除的是当前选中的主机，清除选择
			if ht.selectedHost != nil && ht.selectedHost.ID == hostID {
				ht.ClearSelection()
			}
			break
		}
	}
}

// AddHost 添加新主机到列表
// 遵循模块化规范：数据操作接口
func (ht *HostTable) AddHost(host data.HostInfo) {
	ht.hosts = append(ht.hosts, host)
	ht.list.Refresh()
}

// SetSize 设置列表尺寸
// 遵循模块化规范：UI配置接口
func (ht *HostTable) SetSize(size fyne.Size) {
	ht.container.Resize(size)
	ht.list.Resize(size)
}

// GetSize 获取列表尺寸
// 遵循模块化规范：UI状态接口
func (ht *HostTable) GetSize() fyne.Size {
	return ht.container.Size()
}
