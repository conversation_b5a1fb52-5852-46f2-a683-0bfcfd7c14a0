package services

import (
	"fmt"
	"strings"
	"time"

	"lcheck/core"
	"lcheck/data"
)

// TaskService 任务管理服务
type TaskService struct {
	storage data.Storage
	scanner *core.Scanner
}

// NewTaskService 创建任务管理服务
func NewTaskService(storage data.Storage, scanner *core.Scanner) *TaskService {
	return &TaskService{
		storage: storage,
		scanner: scanner,
	}
}

// CreateTask 创建新任务
func (ts *TaskService) CreateTask(name string, hostIDs []string, concurrency int) (*data.ScanTask, error) {
	if name == "" {
		return nil, fmt.Errorf("任务名称不能为空")
	}

	// 检查任务名称是否重复
	if err := ts.checkDuplicateTaskName(name); err != nil {
		return nil, err
	}

	if len(hostIDs) == 0 {
		return nil, fmt.Errorf("必须选择至少一个主机")
	}

	task := &data.ScanTask{
		ID:        fmt.Sprintf("task_%d", time.Now().Unix()),
		Name:      name,
		Status:    "待执行",
		HostIDs:   hostIDs,
		Progress:  0.0,
		CreatedAt: time.Now(),
		Results:   []data.ScanResult{},
		// 注意：当前数据模型没有Concurrency字段，暂时移除
	}

	return task, nil
}

// LoadTasks 加载所有任务
func (ts *TaskService) LoadTasks() ([]data.ScanTask, error) {
	return ts.storage.LoadTasks()
}

// SaveTasks 保存任务列表
func (ts *TaskService) SaveTasks(tasks []data.ScanTask) error {
	for _, task := range tasks {
		if err := ts.storage.SaveTask(task); err != nil {
			return err
		}
	}
	return nil
}

// SaveTask 保存单个任务
func (ts *TaskService) SaveTask(task *data.ScanTask) error {
	return ts.storage.SaveTask(*task)
}

// DeleteTask 删除任务
func (ts *TaskService) DeleteTask(taskID string) error {
	// 由于数据库外键约束 ON DELETE CASCADE，删除任务时会自动删除相关扫描结果
	return ts.storage.DeleteTask(taskID)
}

// StartTask 启动任务
func (ts *TaskService) StartTask(task *data.ScanTask) error {
	if task.Status != "待执行" {
		return fmt.Errorf("只能启动待执行状态的任务")
	}

	// 更新任务状态
	task.Status = "运行中"
	task.Progress = 0.0
	now := time.Now()
	task.StartedAt = &now

	// 保存任务状态
	if err := ts.SaveTask(task); err != nil {
		return fmt.Errorf("保存任务状态失败: %v", err)
	}

	// 启动实际的扫描任务
	go ts.executeTask(task)

	return nil
}

// executeTask 执行扫描任务
func (ts *TaskService) executeTask(task *data.ScanTask) {
	fmt.Printf("开始执行任务: %s\n", task.Name)
	fmt.Printf("任务HostIDs数量: %d, HostIDs: %v\n", len(task.HostIDs), task.HostIDs)

	// 获取任务中的所有主机信息（包括独立主机和主机组内主机）
	taskHosts, err := ts.getTaskHosts(task.HostIDs)
	if err != nil {
		fmt.Printf("获取任务主机失败: %v\n", err)
		ts.failTask(task, fmt.Sprintf("获取任务主机失败: %v", err))
		return
	}

	if len(taskHosts) == 0 {
		ts.failTask(task, "没有找到有效的主机信息")
		return
	}

	fmt.Printf("找到 %d 个主机进行扫描，实际主机数量: %d\n", len(taskHosts), len(taskHosts))

	// 执行扫描
	successCount := 0
	failureCount := 0

	for i, host := range taskHosts {
		fmt.Printf("正在扫描主机 %d/%d: %s\n", i+1, len(taskHosts), host.Name)

		// 更新进度（开始扫描）
		progress := float64(i) / float64(len(taskHosts))
		task.Progress = progress
		ts.SaveTask(task)

		// 执行扫描
		result := ts.scanHost(host)

		// 统计结果
		if result.Status == "成功" {
			successCount++
		} else {
			failureCount++
		}

		// 添加扫描结果到任务
		task.Results = append(task.Results, result)

		// 保存扫描结果到数据库（需要创建一个带task_id的保存方法）
		err := ts.saveScanResultWithTask(result, task.ID)
		if err != nil {
			fmt.Printf("保存扫描结果失败: %v\n", err)
		}

		// 更新进度（完成扫描）
		progress = float64(i+1) / float64(len(taskHosts))
		task.Progress = progress
		ts.SaveTask(task)

		fmt.Printf("主机 %s 扫描完成，状态: %s，进度: %.1f%%\n",
			host.Name, result.Status, progress*100)
	}

	// 根据扫描结果设置任务状态
	fmt.Printf("扫描统计: 成功 %d, 失败 %d, 总计 %d\n", successCount, failureCount, len(taskHosts))

	if failureCount == 0 {
		// 全部成功
		ts.CompleteTask(task)
		fmt.Printf("任务 %s 执行完成 - 全部主机扫描成功\n", task.Name)
	} else if successCount == 0 {
		// 全部失败
		ts.failTask(task, fmt.Sprintf("所有主机扫描失败 (失败: %d)", failureCount))
		fmt.Printf("任务 %s 执行失败 - 所有主机扫描失败\n", task.Name)
	} else {
		// 部分成功
		task.Status = "部分成功"
		task.Error = fmt.Sprintf("部分主机扫描失败 (成功: %d, 失败: %d)", successCount, failureCount)
		now := time.Now()
		task.CompletedAt = &now
		ts.SaveTask(task)
		fmt.Printf("任务 %s 执行完成 - 部分主机扫描成功\n", task.Name)
	}
}

// scanHost 扫描单个主机
func (ts *TaskService) scanHost(host data.HostInfo) data.ScanResult {
	fmt.Printf("  扫描主机: %s (%s:%s)\n", host.Name, host.Host, host.Port)

	startTime := time.Now()
	result := data.ScanResult{
		ID:        fmt.Sprintf("result_%s_%d", host.ID, startTime.Unix()),
		HostID:    host.ID,
		HostName:  host.Name,
		Host:      host.Host,
		StartTime: startTime,
		Metadata: map[string]string{
			"scan_type": "baseline",
			"version":   "3.2.0",
		},
	}

	// 尝试SSH连接测试
	sshClient := ts.scanner.GetSSHClient()
	err := sshClient.TestConnection(host)

	if err != nil {
		// 连接失败
		fmt.Printf("    ❌ 连接失败: %v\n", err)
		result.Status = "失败"
		result.Progress = 0
		result.Error = fmt.Sprintf("SSH连接失败: %v", err)
		result.EndTime = time.Now()
		result.Duration = time.Since(startTime)
		result.TotalChecks = 1
		result.PassedChecks = 0
		result.FailedChecks = 1
		result.WarningChecks = 0
		result.SkippedChecks = 0
		result.TotalScore = 0
		result.MaxScore = 100

		// 创建一个连接失败的检查结果
		connectionCheck := data.BaselineCheckResult{
			ID:          "CONN_001",
			CheckName:   "SSH连接测试",
			Category:    "连接性检查",
			Status:      "失败",
			Score:       0,
			Risk:        "高",
			Description: "测试SSH连接是否可用",
			Details:     fmt.Sprintf("无法连接到主机 %s:%s", host.Host, host.Port),
			Solution:    "检查主机IP地址、端口、网络连通性和SSH服务状态",
			Reference:   "基础连接检查",
			Command:     fmt.Sprintf("ssh %s@%s -p %s", host.Username, host.Host, host.Port),
			Output:      "",
			Error:       err.Error(),
			Duration:    time.Since(startTime),
			Metadata: map[string]string{
				"host_id":   host.ID,
				"host_name": host.Name,
				"check_id":  "CONN_001",
			},
			CheckedAt: time.Now(),
		}

		result.CheckResults = []data.BaselineCheckResult{connectionCheck}
		return result
	}

	fmt.Printf("    ✓ SSH连接成功，开始基线检查...\n")

	// 创建基线检查器
	checker := core.NewBaselineChecker(sshClient)

	// 执行所有基线检查
	checkResults := checker.RunAllChecks(host)

	// 统计检查结果
	var passedCount, failedCount, warningCount int
	var totalScore, maxScore int

	for _, checkResult := range checkResults {
		switch checkResult.Status {
		case "通过":
			passedCount++
		case "失败":
			failedCount++
		case "警告":
			warningCount++
		}
		totalScore += checkResult.Score
		maxScore += 100 // 每个检查项满分100
	}

	// 设置扫描结果
	result.Status = "成功"
	result.Progress = 100
	result.TotalChecks = len(checkResults)
	result.PassedChecks = passedCount
	result.FailedChecks = failedCount
	result.WarningChecks = warningCount
	result.SkippedChecks = 0
	result.TotalScore = totalScore
	result.MaxScore = maxScore
	result.EndTime = time.Now()
	result.Duration = time.Since(startTime)
	result.CheckResults = checkResults
	result.SystemInfo = &data.SystemInfo{
		Hostname:    host.Name,
		OS:          "Linux",
		OSVersion:   "Ubuntu 20.04",
		CollectedAt: time.Now(),
	}

	fmt.Printf("    ✓ 基线检查完成，总得分: %d/%d，通过: %d，失败: %d，警告: %d\n",
		totalScore, maxScore, passedCount, failedCount, warningCount)

	fmt.Printf("    ✓ 扫描完成，得分: %d/%d\n", result.TotalScore, result.MaxScore)
	return result
}

// failTask 标记任务失败
func (ts *TaskService) failTask(task *data.ScanTask, errorMsg string) {
	task.Status = "失败"
	task.Error = errorMsg
	task.Progress = 1.0 // 设置为100%，表示已完成（虽然失败）
	now := time.Now()
	task.CompletedAt = &now
	ts.SaveTask(task)
	fmt.Printf("任务失败: %s - %s\n", task.Name, errorMsg)
}

// StopTask 停止任务
func (ts *TaskService) StopTask(task *data.ScanTask) error {
	if task.Status != "运行中" {
		return fmt.Errorf("只能停止运行中的任务")
	}

	// 更新任务状态
	task.Status = "已停止"
	now := time.Now()
	task.CompletedAt = &now

	// 保存任务状态
	if err := ts.SaveTask(task); err != nil {
		return fmt.Errorf("保存任务状态失败: %v", err)
	}

	return nil
}

// CompleteTask 完成任务
func (ts *TaskService) CompleteTask(task *data.ScanTask) error {
	// 更新任务状态
	task.Status = "已完成"
	task.Progress = 1.0
	now := time.Now()
	task.CompletedAt = &now

	// 保存任务状态
	if err := ts.SaveTask(task); err != nil {
		return fmt.Errorf("保存任务状态失败: %v", err)
	}

	return nil
}

// UpdateTaskProgress 更新任务进度
func (ts *TaskService) UpdateTaskProgress(task *data.ScanTask, progress float64) error {
	task.Progress = progress

	// 保存任务状态
	if err := ts.SaveTask(task); err != nil {
		return fmt.Errorf("保存任务进度失败: %v", err)
	}

	return nil
}

// AddTaskResult 添加任务结果
func (ts *TaskService) AddTaskResult(task *data.ScanTask, result data.ScanResult) error {
	task.Results = append(task.Results, result)

	// 获取实际的主机数量来计算进度
	taskHosts, err := ts.getTaskHosts(task.HostIDs)
	if err != nil {
		return fmt.Errorf("获取任务主机失败: %v", err)
	}

	// 更新进度 - 使用实际主机数量而不是HostIDs数量
	progress := float64(len(task.Results)) / float64(len(taskHosts))
	task.Progress = progress

	// 如果所有主机都完成了，标记任务为完成
	if len(task.Results) >= len(taskHosts) {
		return ts.CompleteTask(task)
	}

	// 保存任务状态
	if err := ts.SaveTask(task); err != nil {
		return fmt.Errorf("保存任务结果失败: %v", err)
	}

	return nil
}

// GetTaskStatistics 获取任务统计信息
func (ts *TaskService) GetTaskStatistics(task *data.ScanTask) map[string]interface{} {
	stats := map[string]interface{}{
		"totalHosts":     len(task.HostIDs),
		"completedHosts": len(task.Results),
		"progress":       task.Progress * 100,
		"status":         task.Status,
	}

	if task.StartedAt != nil {
		stats["startTime"] = task.StartedAt.Format("2006-01-02 15:04:05")
		if task.CompletedAt != nil {
			stats["duration"] = task.CompletedAt.Sub(*task.StartedAt).String()
		} else {
			stats["elapsed"] = time.Since(*task.StartedAt).String()
		}
	}

	if task.CompletedAt != nil {
		stats["completedTime"] = task.CompletedAt.Format("2006-01-02 15:04:05")
	}

	// 统计检查结果
	totalChecks := 0
	passedChecks := 0
	failedChecks := 0
	warningChecks := 0

	for _, result := range task.Results {
		totalChecks += result.TotalChecks
		passedChecks += result.PassedChecks
		failedChecks += result.FailedChecks
		warningChecks += result.WarningChecks
	}

	stats["totalChecks"] = totalChecks
	stats["passedChecks"] = passedChecks
	stats["failedChecks"] = failedChecks
	stats["warningChecks"] = warningChecks

	return stats
}

// ValidateTask 验证任务配置
func (ts *TaskService) ValidateTask(name string, hostIDs []string, concurrency int) error {
	if name == "" {
		return fmt.Errorf("任务名称不能为空")
	}

	if len(hostIDs) == 0 {
		return fmt.Errorf("必须选择至少一个主机")
	}

	if concurrency < 1 || concurrency > 10 {
		return fmt.Errorf("并发数必须在1-10之间")
	}

	// 验证主机是否存在
	hosts, err := ts.storage.LoadHosts()
	if err != nil {
		return fmt.Errorf("加载主机列表失败: %v", err)
	}

	hostMap := make(map[string]bool)
	for _, host := range hosts {
		hostMap[host.ID] = true
	}

	for _, hostID := range hostIDs {
		if !hostMap[hostID] {
			return fmt.Errorf("主机不存在: %s", hostID)
		}
	}

	return nil
}

// saveScanResultWithTask 保存扫描结果并关联任务ID
func (ts *TaskService) saveScanResultWithTask(result data.ScanResult, taskID string) error {
	// 设置任务ID
	result.TaskID = taskID

	// 直接保存扫描结果（包含任务ID）
	return ts.storage.SaveScanResult(result)
}

// getTaskHosts 获取任务中的所有主机信息（包括独立主机和主机组内主机）
func (ts *TaskService) getTaskHosts(hostIDs []string) ([]data.HostInfo, error) {
	var taskHosts []data.HostInfo

	// 加载独立主机
	independentHosts, err := ts.storage.LoadHosts()
	if err != nil {
		return nil, fmt.Errorf("加载独立主机失败: %v", err)
	}

	// 创建独立主机映射
	independentHostMap := make(map[string]data.HostInfo)
	for _, host := range independentHosts {
		independentHostMap[host.ID] = host
	}

	// 加载所有主机组
	groups, err := ts.storage.LoadGroups()
	if err != nil {
		return nil, fmt.Errorf("加载主机组失败: %v", err)
	}

	// 创建主机组内主机映射
	groupHostMap := make(map[string]data.HostInfo)
	for _, group := range groups {
		for _, host := range group.Hosts {
			groupHostMap[host.ID] = host
		}
	}

	// 根据hostIDs查找主机
	for _, hostID := range hostIDs {
		// 先从独立主机中查找
		if host, exists := independentHostMap[hostID]; exists {
			taskHosts = append(taskHosts, host)
			continue
		}

		// 再从主机组内主机中查找
		if host, exists := groupHostMap[hostID]; exists {
			taskHosts = append(taskHosts, host)
			continue
		}

		// 如果都找不到，记录警告但继续处理其他主机
		fmt.Printf("警告: 未找到主机ID %s 对应的主机信息\n", hostID)
	}

	return taskHosts, nil
}

// GetTaskHosts 公共方法：获取任务中的所有主机信息
func (ts *TaskService) GetTaskHosts(hostIDs []string) ([]data.HostInfo, error) {
	return ts.getTaskHosts(hostIDs)
}

// RestartTask 重启任务 - 重置任务状态并重新启动
func (ts *TaskService) RestartTask(task *data.ScanTask) error {
	// 检查任务状态，只有已完成、失败、已停止、部分成功的任务才能重启
	if task.Status != "已完成" && task.Status != "失败" && task.Status != "已停止" && task.Status != "部分成功" {
		return fmt.Errorf("只能重启已完成、失败、已停止或部分成功的任务")
	}

	fmt.Printf("重启任务前 - 任务: %s, HostIDs数量: %d, HostIDs: %v\n", task.Name, len(task.HostIDs), task.HostIDs)

	// 删除数据库中该任务的所有扫描结果，防止统计叠加
	err := ts.storage.DeleteScanResultsByTask(task.ID)
	if err != nil {
		fmt.Printf("删除任务扫描结果失败: %v\n", err)
		// 不返回错误，继续执行重启流程
	} else {
		fmt.Printf("已删除任务 %s 的历史扫描结果\n", task.Name)
	}

	// 重置任务状态
	task.Status = "待执行"
	task.Progress = 0.0
	task.Error = ""
	task.Results = []data.ScanResult{} // 清空之前的扫描结果
	task.StartedAt = nil
	task.CompletedAt = nil

	fmt.Printf("重启任务后 - 任务: %s, HostIDs数量: %d, HostIDs: %v\n", task.Name, len(task.HostIDs), task.HostIDs)

	// 保存重置后的任务状态
	if err := ts.SaveTask(task); err != nil {
		return fmt.Errorf("保存任务状态失败: %v", err)
	}

	// 立即启动任务
	return ts.StartTask(task)
}

// checkDuplicateTaskName 检查任务名称是否重复
func (ts *TaskService) checkDuplicateTaskName(name string) error {
	tasks, err := ts.storage.LoadTasks()
	if err != nil {
		return fmt.Errorf("检查任务名重复失败: %v", err)
	}

	trimmedName := strings.TrimSpace(name)
	for _, task := range tasks {
		if strings.TrimSpace(task.Name) == trimmedName {
			return fmt.Errorf("任务名 '%s' 已存在，请使用不同的名称", trimmedName)
		}
	}

	return nil
}
