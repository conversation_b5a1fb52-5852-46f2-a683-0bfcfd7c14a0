package checkers

import (
	"fmt"
	"strings"
	"time"

	"lcheck/data"
)

// SecurityChecker 安全配置检查器 - 使用安全且兼容的命令
type SecurityChecker struct {
	name     string
	category string
}

// NewSecurityChecker 创建安全配置检查器
func NewSecurityChecker() *SecurityChecker {
	return &SecurityChecker{
		name:     "安全配置检查器",
		category: "系统安全",
	}
}

// GetName 获取检查器名称
func (sc *SecurityChecker) GetName() string {
	return sc.name
}

// GetCategory 获取检查器类别
func (sc *SecurityChecker) GetCategory() string {
	return sc.category
}

// GetChecks 获取安全配置相关的所有检查项
func (sc *SecurityChecker) GetChecks() []data.BaselineCheck {
	return []data.BaselineCheck{
		{
			ID:          "SEC_USER_CHECK",
			Name:        "用户账户安全检查",
			Category:    "用户安全",
			Description: "检查系统用户账户配置",
			Risk:        "中",
			Solution:    "删除不必要的用户账户，设置强密码策略",
			Reference:   "CIS Linux Benchmark 5.4",
			CheckFunc: func(client interface{}, host data.HostInfo) data.BaselineCheckResult {
				return sc.checkUserSecurity(client.(SSHExecutor), host)
			},
		},
		{
			ID:          "SEC_FILE_PERM",
			Name:        "关键文件权限检查",
			Category:    "文件安全",
			Description: "检查关键系统文件权限设置",
			Risk:        "高",
			Solution:    "修正关键文件权限设置",
			Reference:   "CIS Linux Benchmark 6.1",
			CheckFunc: func(client interface{}, host data.HostInfo) data.BaselineCheckResult {
				return sc.checkFilePermissions(client.(SSHExecutor), host)
			},
		},
		{
			ID:          "SEC_NETWORK",
			Name:        "网络安全配置检查",
			Category:    "网络安全",
			Description: "检查网络安全配置",
			Risk:        "中",
			Solution:    "优化网络安全配置",
			Reference:   "CIS Linux Benchmark 3.1",
			CheckFunc: func(client interface{}, host data.HostInfo) data.BaselineCheckResult {
				return sc.checkNetworkSecurity(client.(SSHExecutor), host)
			},
		},
	}
}

// RunAllChecks 执行所有安全检查项
func (sc *SecurityChecker) RunAllChecks(sshClient SSHExecutor, host data.HostInfo) ([]data.BaselineCheckResult, error) {
	var results []data.BaselineCheckResult
	checks := sc.GetChecks()

	for _, check := range checks {
		// 添加panic恢复机制
		func() {
			defer func() {
				if r := recover(); r != nil {
					fmt.Printf("安全检查项 %s 执行时发生panic: %v\n", check.ID, r)
					// 创建一个错误结果
					errorResult := data.BaselineCheckResult{
						ID:          check.ID,
						CheckName:   check.Name,
						Category:    check.Category,
						Status:      "失败",
						Score:       0,
						Risk:        check.Risk,
						Description: check.Description,
						Details:     fmt.Sprintf("检查执行失败: %v", r),
						Solution:    check.Solution,
						Reference:   check.Reference,
						Error:       fmt.Sprintf("panic: %v", r),
					}
					results = append(results, errorResult)
				}
			}()

			result := check.CheckFunc(sshClient, host)
			result.CheckName = check.Name
			result.Category = check.Category
			result.Description = check.Description
			result.Risk = check.Risk
			result.Solution = check.Solution
			result.Reference = check.Reference
			results = append(results, result)
		}()
	}

	return results, nil
}

// RunCheck 执行单个检查项 - 实现Checker接口
func (sc *SecurityChecker) RunCheck(sshClient SSHExecutor, host data.HostInfo, checkID string) (*data.BaselineCheckResult, error) {
	checks := sc.GetChecks()

	for _, check := range checks {
		if check.ID == checkID {
			result := check.CheckFunc(sshClient, host)
			result.CheckName = check.Name
			result.Category = check.Category
			result.Description = check.Description
			result.Risk = check.Risk
			result.Solution = check.Solution
			result.Reference = check.Reference
			return &result, nil
		}
	}

	return nil, fmt.Errorf("检查项 %s 不存在", checkID)
}

// checkUserSecurity 检查用户账户安全 - 使用安全的只读命令
func (sc *SecurityChecker) checkUserSecurity(sshClient SSHExecutor, host data.HostInfo) data.BaselineCheckResult {
	startTime := time.Now()
	
	result := data.BaselineCheckResult{
		ID:        "SEC_USER_CHECK",
		CheckName: "用户账户安全检查",
		Category:  "用户安全",
		CheckedAt: time.Now(),
		Metadata: map[string]string{
			"host_id":   host.ID,
			"host_name": host.Name,
			"check_id":  "SEC_USER_CHECK",
		},
	}

	// 安全的用户检查命令 - 只读操作
	command := "(cat /etc/passwd 2>/dev/null | grep -E '^[^:]*:[^:]*:0:' | wc -l) && echo '---' && (cat /etc/passwd 2>/dev/null | grep -E '/bin/(bash|sh|zsh|csh|tcsh|fish)$' | wc -l) && echo '---' && (cat /etc/shadow 2>/dev/null | grep -E '^[^:]*::' | wc -l 2>/dev/null || echo '0') && echo '---' && (last -n 5 2>/dev/null | head -5 || echo 'No login history')"
	
	result.Command = command
	output, err := sshClient.ExecuteCommand(host, command)
	
	result.Output = output
	result.Duration = time.Since(startTime)
	
	if err != nil {
		result.Status = "失败"
		result.Score = 0
		result.Error = fmt.Sprintf("执行命令失败: %v", err)
		result.Details = "无法检查用户账户安全配置"
		return result
	}

	// 解析结果
	lines := strings.Split(strings.TrimSpace(output), "---")
	if len(lines) >= 3 {
		rootUsers := strings.TrimSpace(lines[0])
		shellUsers := strings.TrimSpace(lines[1])
		emptyPassUsers := strings.TrimSpace(lines[2])
		
		result.Metadata["rootUsers"] = rootUsers
		result.Metadata["shellUsers"] = shellUsers
		result.Metadata["emptyPassUsers"] = emptyPassUsers
		
		// 评分逻辑
		score := 100
		if rootUsers != "1" {
			score -= 30 // 多个root用户扣分
		}
		if emptyPassUsers != "0" {
			score -= 50 // 空密码用户严重扣分
		}
		
		result.Score = score
		if score >= 80 {
			result.Status = "通过"
			result.Details = "用户账户安全配置良好"
		} else if score >= 60 {
			result.Status = "警告"
			result.Details = "用户账户安全配置存在风险，建议优化"
		} else {
			result.Status = "失败"
			result.Details = "用户账户安全配置存在严重问题"
		}
	} else {
		result.Status = "警告"
		result.Score = 50
		result.Details = "用户账户信息收集不完整"
	}

	return result
}

// checkFilePermissions 检查关键文件权限 - 使用安全的只读命令
func (sc *SecurityChecker) checkFilePermissions(sshClient SSHExecutor, host data.HostInfo) data.BaselineCheckResult {
	startTime := time.Now()
	
	result := data.BaselineCheckResult{
		ID:        "SEC_FILE_PERM",
		CheckName: "关键文件权限检查",
		Category:  "文件安全",
		CheckedAt: time.Now(),
		Metadata: map[string]string{
			"host_id":   host.ID,
			"host_name": host.Name,
			"check_id":  "SEC_FILE_PERM",
		},
	}

	// 安全的文件权限检查命令 - 只读操作
	command := "(ls -l /etc/passwd /etc/shadow /etc/group /etc/gshadow 2>/dev/null | awk '{print $1,$9}') || echo 'Files not accessible'"
	
	result.Command = command
	output, err := sshClient.ExecuteCommand(host, command)
	
	result.Output = output
	result.Duration = time.Since(startTime)
	
	if err != nil {
		result.Status = "失败"
		result.Score = 0
		result.Error = fmt.Sprintf("执行命令失败: %v", err)
		result.Details = "无法检查文件权限"
		return result
	}

	// 简单的权限检查
	score := 100
	if strings.Contains(output, "Files not accessible") {
		result.Status = "警告"
		result.Score = 50
		result.Details = "无法访问关键系统文件"
	} else {
		// 检查是否有过于宽松的权限
		if strings.Contains(output, "rw-rw-rw-") || strings.Contains(output, "rwxrwxrwx") {
			score -= 50
		}
		
		result.Score = score
		if score >= 80 {
			result.Status = "通过"
			result.Details = "关键文件权限配置正常"
		} else {
			result.Status = "失败"
			result.Details = "发现文件权限配置问题"
		}
	}

	return result
}

// checkNetworkSecurity 检查网络安全配置 - 使用安全的只读命令
func (sc *SecurityChecker) checkNetworkSecurity(sshClient SSHExecutor, host data.HostInfo) data.BaselineCheckResult {
	startTime := time.Now()
	
	result := data.BaselineCheckResult{
		ID:        "SEC_NETWORK",
		CheckName: "网络安全配置检查",
		Category:  "网络安全",
		CheckedAt: time.Now(),
		Metadata: map[string]string{
			"host_id":   host.ID,
			"host_name": host.Name,
			"check_id":  "SEC_NETWORK",
		},
	}

	// 安全的网络配置检查命令 - 只读操作
	command := "(cat /proc/sys/net/ipv4/ip_forward 2>/dev/null || echo '0') && echo '---' && (cat /proc/sys/net/ipv4/icmp_echo_ignore_all 2>/dev/null || echo '0') && echo '---' && (iptables -L -n 2>/dev/null | grep -c 'Chain' || echo '0')"
	
	result.Command = command
	output, err := sshClient.ExecuteCommand(host, command)
	
	result.Output = output
	result.Duration = time.Since(startTime)
	
	if err != nil {
		result.Status = "失败"
		result.Score = 0
		result.Error = fmt.Sprintf("执行命令失败: %v", err)
		result.Details = "无法检查网络安全配置"
		return result
	}

	// 解析网络配置
	lines := strings.Split(strings.TrimSpace(output), "---")
	score := 100
	
	if len(lines) >= 2 {
		ipForward := strings.TrimSpace(lines[0])
		icmpIgnore := strings.TrimSpace(lines[1])
		
		result.Metadata["ipForward"] = ipForward
		result.Metadata["icmpIgnore"] = icmpIgnore
		
		// IP转发应该被禁用（除非是路由器）
		if ipForward == "1" {
			score -= 20
		}
		
		result.Score = score
		if score >= 80 {
			result.Status = "通过"
			result.Details = "网络安全配置良好"
		} else {
			result.Status = "警告"
			result.Details = "网络安全配置需要优化"
		}
	} else {
		result.Status = "警告"
		result.Score = 50
		result.Details = "网络配置信息收集不完整"
	}

	return result
}
